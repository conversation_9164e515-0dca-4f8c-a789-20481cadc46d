#Requires AutoHotkey v2.0

; 尝试加载UIA库，如果失败则使用备用方案
global UIA_AVAILABLE := false
global UIA_INSTANCE := ""  ; 用于存储UIA实例的变量
global windows := [], scanResults := []

try {
    ; 首先尝试加载UIA库 - 使用相对路径
    ; 假设UIA库在上级目录的UIA-v2-1.1.0\Lib\文件夹中
    #Include *i ..\UIA-v2-1.1.0\Lib\UIA.ahk
    
    ; 检查UIA类是否可用
    if !UIA.HasMethod("GetRootElement") {
        throw Error("UIA类缺少必要方法", -1)
    }

    ; 测试UIA是否真正可用
    testElement := UIA.GetRootElement()
    if testElement && IsObject(testElement) {
        UIA_AVAILABLE := true
        MsgBox("UIA库加载成功！`n`n支持完整的控件监控功能", "成功", 0)
    } else {
        throw Error("UIA库加载但无法获取根元素", -1)
    }
} catch as e {
    MsgBox("UIA库加载失败: " e.message "`n`n可能的原因：`n1. UIA库文件未找到`n2. 路径设置不正确`n3. 系统不支持UIA`n`n将使用基本窗口监控模式", "警告", 0)
    UIA_AVAILABLE := false
}

CreateMainGUI()

CreateMainGUI() {
    global listBox, infoText, editSearch, textStat, windows
    winGui := Gui("+Resize", "UIA 控件结构检查器")

    winGui.AddText("x10 y10 w300 h20", "选择目标窗口：")
    listBox := winGui.AddListBox("x10 y30 w300 h100 vlistBox", [])

    btnRefresh := winGui.AddButton("x320 y30 w100 h30", "刷新窗口")
    btnSimple := winGui.AddButton("x430 y30 w100 h30", "简单模式")
    btnTest := winGui.AddButton("x540 y30 w80 h30", "UIA诊断")
    btnScan := winGui.AddButton("x320 y70 w100 h30", "扫描结构")
    btnExport := winGui.AddButton("x320 y110 w100 h30", "导出结果")

    winGui.AddText("x10 y140 w300 h20", "搜索关键词（Name 或 Type）：")
    editSearch := winGui.AddEdit("x10 y160 w300 h30")
    btnSearch := winGui.AddButton("x320 y160 w100 h30", "搜索")

    winGui.AddText("x10 y200 w420 h20", "控件信息/搜索结果：")
    infoText := winGui.AddEdit("x10 y220 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnStat := winGui.AddButton("x10 y380 w200 h30", "类型统计")
    textStat := winGui.AddEdit("x10 y420 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnMonitor := winGui.AddButton("x220 y380 w100 h30", "悬停监控")
    btnStopMonitor := winGui.AddButton("x330 y380 w100 h30", "停止监控")

    btnRefresh.OnEvent("Click", RefreshWindowList)
    btnSimple.OnEvent("Click", SimpleWindowList)
    btnTest.OnEvent("Click", DiagnoseUIA)
    btnScan.OnEvent("Click", ScanUIA)
    btnExport.OnEvent("Click", ExportResult)
    btnSearch.OnEvent("Click", SearchControls)
    btnStat.OnEvent("Click", ShowControlTypeStat)
    btnMonitor.OnEvent("Click", StartMouseMonitor)
    btnStopMonitor.OnEvent("Click", StopMouseMonitor)

    RefreshWindowList()
    winGui.Show("w630 h600")
}

RefreshWindowList(*) {
    global windows, listBox
    windows := []
    listBox.Delete()

    windowCount := 0
    addedCount := 0
    uiaCount := 0

    try {
        windowList := WinGetList()

        for hwnd in windowList {
            windowCount++
            try {
                title := WinGetTitle(hwnd)
                className := WinGetClass(hwnd)
                processName := WinGetProcessName(hwnd)

                ; 检查窗口是否真实可见且适合UIA监控
                if !IsMonitorableWindow(hwnd, title, className, processName) {
                    continue
                }

                ; 创建显示标题
                displayTitle := title
                if displayTitle == "" {
                    displayTitle := "[" . className . "] - " . processName
                }

                ; 尝试创建UIA元素
                winElement := ""
                uiaSupported := false
                try {
                    if UIA_AVAILABLE {
                        winElement := UIA.ElementFromHandle(hwnd)
                        if winElement {
                            uiaSupported := true
                            uiaCount++
                            displayTitle .= " [UIA]"
                        }
                    }
                } catch {
                    winElement := "BasicMode"
                    displayTitle .= " [基本]"
                }

                ; 添加到数组
                windows.Push({
                    Title: displayTitle,
                    Win: winElement,
                    Hwnd: hwnd,
                    ClassName: className,
                    ProcessName: processName,
                    UIASupported: uiaSupported
                })

                ; 添加到列表框
                try {
                    listBox.Add([displayTitle])
                    addedCount++
                } catch {
                    continue
                }

            } catch {
                continue
            }
        }
    } catch as e {
        MsgBox("获取窗口列表失败: " . e.message)
        return
    }

    MsgBox("扫描完成！`n总窗口: " . windowCount . "`n可监控: " . addedCount . "`nUIA支持: " . uiaCount . "`n`n带[UIA]标记的窗口支持完整控件扫描")
}

; 判断是否为可监控的窗口（适合UIA监控）
IsMonitorableWindow(hwnd, title, className, processName) {
    ; 必须是可见窗口
    try {
        if !(WinGetStyle(hwnd) & 0x10000000) {  ; WS_VISIBLE
            return false
        }
    } catch {
        return false
    }

    ; 排除系统窗口
    if title == "Program Manager" || title == "Desktop" || title == "Task Switching" {
        return false
    }

    ; 排除系统进程
    excludeProcesses := ["dwm.exe", "winlogon.exe", "csrss.exe", "wininit.exe", "services.exe", "lsass.exe"]
    for proc in excludeProcesses {
        if processName == proc {
            return false
        }
    }

    ; 排除系统窗口类
    excludeClasses := ["Shell_TrayWnd", "DV2ControlHost", "MsgrIMEWindowClass", "SysShadow", "IME", "MSCTFIME UI"]
    for cls in excludeClasses {
        if InStr(className, cls) {
            return false
        }
    }

    ; 必须有合理的大小
    try {
        WinGetPos(&x, &y, &w, &h, hwnd)
        if w < 100 || h < 50 {
            return false
        }
    } catch {
        return false
    }

    ; 优先显示有标题的窗口
    if title != "" {
        return true
    }

    ; 或者是已知的应用程序窗口类
    appClasses := ["Chrome_WidgetWin_1", "Notepad", "ApplicationFrameWindow", "CabinetWClass", "HwndWrapper", "Window"]
    for cls in appClasses {
        if InStr(className, cls) {
            return true
        }
    }

    ; 或者是常见的开发工具
    devProcesses := ["Code.exe", "devenv.exe", "notepad++.exe", "sublime_text.exe", "atom.exe"]
    for proc in devProcesses {
        if processName == proc {
            return true
        }
    }

    return false
}

SimpleWindowList(*) {
    global windows, listBox
    windows := []
    listBox.Delete()

    ; 简单模式：不使用UIA，只获取基本窗口信息
    windowCount := 0
    addedCount := 0

    try {
        windowList := WinGetList()

        for hwnd in windowList {
            windowCount++
            try {
                title := WinGetTitle(hwnd)
                className := WinGetClass(hwnd)
                processName := WinGetProcessName(hwnd)

                ; 简单模式：显示所有有意义的窗口
                shouldAdd := false
                displayTitle := ""

                if title != "" && title != "Program Manager" && title != "Desktop" {
                    displayTitle := title . " [简单]"
                    shouldAdd := true
                } else if className != "" && processName != "" &&
                         processName != "dwm.exe" && processName != "winlogon.exe" {
                    displayTitle := "[" . className . "] - " . processName . " [简单]"
                    shouldAdd := true
                }

                if shouldAdd && displayTitle != "" {
                    ; 不使用UIA，直接添加基本信息
                    windows.Push({Title: displayTitle, Win: "SimpleMode", Hwnd: hwnd, ClassName: className, ProcessName: processName})

                    ; 使用正确的语法添加到列表框 - 使用数组
                    try {
                        listBox.Add([displayTitle])
                        addedCount++
                    } catch {
                        ; 如果添加失败，跳过这个窗口
                        continue
                    }
                }
            } catch {
                continue
            }
        }
    } catch as e {
        MsgBox("简单模式失败: " . e.message)
        return
    }

    MsgBox("简单模式扫描完成！`n总窗口数: " . windowCount . "`n添加窗口数: " . addedCount . "`n`n注意：此模式下某些功能可能受限")
}

TestListBox(*) {
    global listBox, windows

    ; 清空并测试基本功能
    listBox.Delete()
    windows := []

    ; 添加测试项目
    testItems := ["测试项目1", "测试项目2", "记事本", "计算器", "文件资源管理器"]
    addedCount := 0

    for item in testItems {
        try {
            ; 使用正确的语法添加项目 - 使用数组
            listBox.Add([item])
            windows.Push({Title: item, Win: "TestMode", Hwnd: 0, ClassName: "Test", ProcessName: "test.exe"})
            addedCount++
        } catch as e {
            MsgBox("添加项目失败: " . item . "`n错误: " . e.message)
        }
    }

    MsgBox("测试完成！尝试添加 " . testItems.Length . " 个项目，成功添加 " . addedCount . " 个`n请检查列表框是否显示了这些项目")
}

ScanUIA(*) {
    global listBox, windows, scanResults, infoText, UIA_AVAILABLE
    selected := listBox.Value
    if !selected {
        MsgBox("请选择一个窗口")
        return
    }

    selectedWindow := windows[selected]

    ; 如果UIA不可用，使用备用扫描方法
    if !UIA_AVAILABLE {
        ScanBasicWindowInfo(selectedWindow)
        return
    }

    ; 检查是否支持UIA
    if !selectedWindow.UIASupported {
        MsgBox("选中的窗口不支持UIA扫描。`n`n建议：`n1. 选择带[UIA]标记的窗口`n2. 使用'悬停监控'功能`n3. 尝试基本窗口信息扫描")
        ScanBasicWindowInfo(selectedWindow)
        return
    }

    ; 开始UIA扫描
    infoText.Value := "正在扫描控件结构，请稍候..."
    scanResults := []

    try {
        winElement := selectedWindow.Win
        if winElement && winElement != "BasicMode" {
            ; 清空之前的结果
            scanResults := []

            ; 显示调试信息
            infoText.Value := "开始扫描控件...`n正在测试UIA方法..."

            ; 测试多种UIA扫描方法
            methods := []

            ; 方法1：FindAll (获取所有后代元素)
            try {
                descendants := winElement.FindAll(UIA.TrueCondition, UIA.TreeScope.Descendants)
                if descendants {
                    methods.Push("FindAll(Descendants): " . descendants.Length . " 个控件")

                    ; 处理前200个控件作为测试
                    maxProcess := Min(descendants.Length, 200)
                    Loop maxProcess {
                        try {
                            descendant := descendants[A_Index]
                            AddControlToResults(descendant, "Descendant_" . A_Index)
                        } catch {
                            continue
                        }
                    }
                } else {
                    methods.Push("FindAll(Descendants): 失败")
                }
            } catch as e {
                methods.Push("FindAll(Descendants): 错误 - " . e.message)
            }

            ; 方法2：GetChildren (获取直接子控件)
            try {
                children := winElement.GetChildren()
                if children {
                    methods.Push("GetChildren: " . children.Length . " 个直接子控件")

                    for child in children {
                        try {
                            AddControlToResults(child, "Child")
                            ; 递归获取子控件的子控件
                            try {
                                grandChildren := child.GetChildren()
                                if grandChildren {
                                    for grandChild in grandChildren {
                                        AddControlToResults(grandChild, "GrandChild")
                                    }
                                }
                            } catch {
                                ; 忽略获取孙子控件的错误
                            }
                        } catch {
                            continue
                        }
                    }
                } else {
                    methods.Push("GetChildren: 失败")
                }
            } catch as e {
                methods.Push("GetChildren: 错误 - " . e.message)
            }

            ; 方法3：使用条件查找按钮
            try {
                ; 查找所有Button类型的控件
                buttonCondition := UIA.CreatePropertyCondition(UIA.Property.ControlType, UIA.ControlType.Button)
                if buttonCondition {
                    buttons := winElement.FindAll(buttonCondition, UIA.TreeScope.Descendants)
                    if buttons {
                        methods.Push("按钮控件: " . buttons.Length . " 个")
                        for button in buttons {
                            AddControlToResults(button, "Button")
                        }
                    }
                }
            } catch as e {
                methods.Push("按钮查找: 错误 - " . e.message)
            }

            ; 显示测试结果
            debugInfo := "=== UIA方法测试结果 ===`n"
            for method in methods {
                debugInfo .= method . "`n"
            }
            debugInfo .= "`n总共找到控件: " . scanResults.Length . " 个`n`n"

            infoText.Value := debugInfo

            ; 生成扫描结果 - 显示控件树结构
            info := "=== UIA控件树扫描结果 ===`n"
            info .= "窗口: " . selectedWindow.Title . "`n"
            info .= "找到控件: " . scanResults.Length . " 个`n"
            maxDepth := 0
            for ctrl in scanResults {
                if ctrl.Depth > maxDepth {
                    maxDepth := ctrl.Depth
                }
            }
            info .= "扫描深度: " . maxDepth . " 层`n`n"

            if scanResults.Length > 0 {
                info .= "=== 控件树结构 ===`n"

                ; 按深度排序显示，形成树状结构
                for i, ctrl in scanResults {
                    ; 显示树状结构
                    info .= ctrl.Path

                    ; 添加详细信息（在同一行或下一行）
                    details := ""
                    if ctrl.Name {
                        details .= " 名称:" . ctrl.Name
                    }
                    if ctrl.Value {
                        details .= " 值:" . ctrl.Value
                    }
                    if ctrl.AutomationId {
                        details .= " ID:" . ctrl.AutomationId
                    }
                    if ctrl.ClassName {
                        details .= " 类:" . ctrl.ClassName
                    }

                    if details {
                        info .= " |" . details
                    }
                    info .= "`n"

                    ; 限制显示数量，避免界面过载
                    if i >= 100 {
                        info .= "`n... 还有 " . (scanResults.Length - 100) . " 个控件`n"
                        info .= "提示：使用搜索功能查找特定控件`n"
                        break
                    }
                }

                ; 添加统计信息
                info .= "`n=== 控件类型统计 ===`n"
                typeStats := Map()
                for ctrl in scanResults {
                    type := ctrl.Type
                    typeStats[type] := typeStats.Has(type) ? typeStats[type] + 1 : 1
                }

                for type, count in typeStats {
                    info .= type . ": " . count . " 个`n"
                }

            } else {
                info .= "未找到任何控件`n`n"
                info .= "可能原因：`n"
                info .= "1. 窗口不支持UIA`n"
                info .= "2. 窗口正在加载中`n"
                info .= "3. 需要管理员权限"
            }

            infoText.Value := info
        } else {
            MsgBox("无法获取窗口的UIA元素")
        }
    } catch as e {
        MsgBox("扫描失败: " . e.message . "`n`n可能原因：`n1. 窗口已关闭`n2. 程序不支持UIA`n3. 权限不足")
        infoText.Value := "扫描失败: " . e.message
    }
}

TraverseElement(element, path := "", depth := 0) {
    global scanResults

    ; 增加递归深度限制，但允许更深的遍历
    if depth > 20 {
        return
    }

    try {
        ; 获取元素信息
        name := ""
        type := ""
        value := ""
        automationId := ""
        className := ""

        try {
            name := element.Name
        } catch {
            name := ""
        }
        try {
            type := element.ControlType
        } catch {
            type := ""
        }
        try {
            value := element.Value
        } catch {
            value := ""
        }
        try {
            automationId := element.AutomationId
        } catch {
            automationId := ""
        }
        try {
            className := element.ClassName
        } catch {
            className := ""
        }

        ; 创建更详细的路径显示
        indent := ""
        Loop depth {
            indent .= "  "  ; 每层缩进2个空格
        }

        fullPath := indent . (type ? type : "Unknown")
        if name {
            fullPath .= "[" . name . "]"
        }
        if automationId {
            fullPath .= "{" . automationId . "}"
        }

        ; 添加到结果（包括所有元素，不只是有类型的）
        scanResults.Push({
            Path: fullPath,
            Type: type ? type : "Unknown",
            Name: name,
            Value: value,
            AutomationId: automationId,
            ClassName: className,
            Depth: depth,
            Element: element
        })

        ; 递归遍历子元素 - 使用正确的UIA方法
        try {
            ; 方法1：使用GetChildren获取直接子元素
            children := element.GetChildren()
            if children && children.Length > 0 {
                for child in children {
                    TraverseElement(child, fullPath, depth + 1)
                }
            }
        } catch {
            ; 如果GetChildren失败，尝试FindAll
            try {
                children := element.FindAll(UIA.TrueCondition, UIA.TreeScope.Children)
                if children && children.Length > 0 {
                    for child in children {
                        TraverseElement(child, fullPath, depth + 1)
                    }
                }
            } catch {
                ; 忽略错误，继续处理其他元素
            }
        }

    } catch as e {
        ; 记录错误但继续处理
        scanResults.Push({
            Path: "ERROR: " . e.message,
            Type: "Error",
            Name: "",
            Value: "",
            AutomationId: "",
            ClassName: "",
            Depth: depth,
            Element: ""
        })
    }
}

ExportResult(*) {
    global scanResults, windows, listBox

    ; 检查是否有扫描结果
    if scanResults.Length = 0 {
        MsgBox("请先进行结构扫描！`n`n步骤：`n1. 选择一个窗口`n2. 点击'扫描结构'`n3. 等待扫描完成`n4. 再次点击'导出结果'")
        return
    }

    ; 选择保存文件
    saveFile := FileSelect("S", "UIA_Controls_" . A_Now . ".txt", "保存UIA控件信息", "Text Files (*.txt)")
    if !saveFile {
        return
    }

    ; 生成详细的导出内容
    try {
        selected := listBox.Value
        selectedWindow := selected ? windows[selected] : {Title: "未知窗口"}

        content := "=== UIA控件扫描报告 ===`n"
        content .= "扫描时间: " . FormatTime(A_Now, "yyyy-MM-dd HH:mm:ss") . "`n"
        content .= "目标窗口: " . selectedWindow.Title . "`n"
        content .= "控件总数: " . scanResults.Length . "`n"
        content .= "=" . StrReplace(Format("{:50s}", ""), " ", "=") . "`n`n"

        ; 添加每个控件的详细信息
        for i, item in scanResults {
            content .= "控件 #" . i . "`n"
            content .= "路径: " . item.Path . "`n"
            content .= "类型: " . item.Type . "`n"
            content .= "名称: " . (item.Name ? item.Name : "[无名称]") . "`n"
            content .= "值: " . (item.Value ? item.Value : "[无值]") . "`n"
            content .= "自动化ID: " . (item.AutomationId ? item.AutomationId : "[无ID]") . "`n"
            content .= "-" . StrReplace(Format("{:50s}", ""), " ", "-") . "`n"
        }

        content .= "`n=== 报告结束 ===`n"

        ; 保存文件
        FileAppend(content, saveFile, "UTF-8")
        MsgBox("导出完成！`n`n文件: " . saveFile . "`n控件数量: " . scanResults.Length)

    } catch as e {
        MsgBox("导出失败: " . e.message)
    }
}

SearchControls(*) {
    global scanResults, editSearch, infoText
    keyword := editSearch.Value
    if keyword = "" {
        MsgBox("请输入搜索关键词`n`n可以搜索：`n- 控件名称`n- 控件类型`n- 自动化ID`n- 控件值")
        return
    }

    if scanResults.Length = 0 {
        MsgBox("请先进行结构扫描")
        return
    }

    matches := ""
    matchCount := 0

    for i, item in scanResults {
        ; 在多个字段中搜索
        if InStr(item.Name, keyword) || InStr(item.Type, keyword) ||
           InStr(item.AutomationId, keyword) || InStr(item.Value, keyword) ||
           InStr(item.Path, keyword) {
            matchCount++
            matches .= "匹配 #" . matchCount . "`n"
            matches .= "路径: " . item.Path . "`n"
            matches .= "类型: " . item.Type . "`n"
            matches .= "名称: " . (item.Name ? item.Name : "[无名称]") . "`n"
            matches .= "值: " . (item.Value ? item.Value : "[无值]") . "`n"
            matches .= "自动化ID: " . (item.AutomationId ? item.AutomationId : "[无ID]") . "`n"
            matches .= "`n"
        }
    }

    if matches != "" {
        infoText.Value := "=== 搜索结果 ===`n关键词: " . keyword . "`n找到: " . matchCount . " 个匹配项`n`n" . matches
    } else {
        infoText.Value := "未找到包含 '" . keyword . "' 的控件`n`n提示：`n- 检查拼写`n- 尝试部分关键词`n- 搜索控件类型（如Button、Edit等）"
    }
}

StartMouseMonitor(*) {
    SetTimer(WatchUnderMouse, 200)
    MsgBox("鼠标悬停监控已启动（每 200ms 更新一次）`n移动鼠标到任意控件上查看信息")
}

StopMouseMonitor(*) {
    SetTimer(WatchUnderMouse, 0)
    MsgBox("鼠标悬停监控已停止")
}

WatchUnderMouse() {
    global infoText, UIA_AVAILABLE
    try {
        ; 获取鼠标位置
        MouseGetPos(&x, &y, &hwnd)

        info := "=== 实时控件监控 ===`n"
        info .= "鼠标位置: (" . x . ", " . y . ")`n`n"

        ; 如果UIA可用，尝试获取UIA元素
        if UIA_AVAILABLE {
            try {
                element := UIA.ElementFromPoint()
                if element {
                    info .= "=== UIA控件信息 ===`n"

                    ; 获取控件属性
                try {
                    name := element.Name
                    info .= "名称: " . (name ? name : "[无名称]") . "`n"
                } catch {
                    info .= "名称: [获取失败]`n"
                }

                try {
                    type := element.ControlType
                    info .= "类型: " . (type ? type : "[未知类型]") . "`n"
                } catch {
                    info .= "类型: [获取失败]`n"
                }

                try {
                    value := element.Value
                    info .= "值: " . (value ? value : "[无值]") . "`n"
                } catch {
                    info .= "值: [获取失败]`n"
                }

                try {
                    automationId := element.AutomationId
                    info .= "自动化ID: " . (automationId ? automationId : "[无ID]") . "`n"
                } catch {
                    info .= "自动化ID: [获取失败]`n"
                }

                try {
                    className := element.ClassName
                    info .= "类名: " . (className ? className : "[无类名]") . "`n"
                } catch {
                    info .= "类名: [获取失败]`n"
                }

                info .= "`n"
            } else {
                info .= "=== 无UIA控件 ===`n"
            }
            } catch as e {
                info .= "=== UIA获取失败 ===`n"
                info .= "错误: " . e.message . "`n`n"
            }
        } else {
            info .= "=== UIA不可用 ===`n"
            info .= "使用基本窗口监控模式`n`n"
        }

        ; 显示窗口基本信息
        if hwnd {
            try {
                title := WinGetTitle(hwnd)
                className := WinGetClass(hwnd)
                processName := WinGetProcessName(hwnd)

                info .= "=== 窗口信息 ===`n"
                info .= "标题: " . (title ? title : "[无标题]") . "`n"
                info .= "类名: " . className . "`n"
                info .= "进程: " . processName . "`n"
                info .= "句柄: " . hwnd . "`n"

            } catch as e {
                info .= "获取窗口信息失败: " . e.message . "`n"
            }
        } else {
            info .= "=== 无窗口 ===`n"
        }

        infoText.Value := info

    } catch as e {
        infoText.Value := "监控错误: " . e.message
    }
}

ShowControlTypeStat(*) {
    global scanResults, textStat
    stat := Map()
    for item in scanResults {
        type := item.Type
        stat[type] := stat.Has(type) ? stat[type] + 1 : 1
    }
    list := []
    for k, v in stat {
        list.Push({Type: k, Count: v})
    }
    SortArrayByCount(list)
    output := ""
    for item in list {
        output .= item.Type ": " item.Count "`n"
    }
    textStat.Value := output
}

SortArrayByCount(arr) {
    if !arr || arr.Length <= 1
        return arr
    n := arr.Length
    Loop n - 1 {
        i := A_Index
        Loop n - i {
            j := A_Index
            if (j + 1) > n
                continue
            a := arr[j]
            b := arr[j + 1]
            if a.HasProp("Count") && b.HasProp("Count") {
                if a.Count < b.Count {
                    temp := arr[j]
                    arr[j] := arr[j + 1]
                    arr[j + 1] := temp
                }
            }
        }
    }
    return arr
}

; 添加控件到扫描结果的辅助函数
AddControlToResults(element, prefix := "") {
    global scanResults

    try {
        ; 获取控件信息
        name := ""
        type := ""
        value := ""
        automationId := ""
        className := ""

        try {
            name := element.Name
        } catch {
            name := ""
        }
        try {
            type := element.ControlType
        } catch {
            type := ""
        }
        try {
            value := element.Value
        } catch {
            value := ""
        }
        try {
            automationId := element.AutomationId
        } catch {
            automationId := ""
        }
        try {
            className := element.ClassName
        } catch {
            className := ""
        }

        ; 创建显示路径
        path := prefix ? prefix . ": " : ""
        path .= type ? type : "Unknown"
        if name {
            path .= "[" . name . "]"
        }
        if automationId {
            path .= "{" . automationId . "}"
        }

        ; 添加到结果
        scanResults.Push({
            Path: path,
            Type: type ? type : "Unknown",
            Name: name,
            Value: value,
            AutomationId: automationId,
            ClassName: className,
            Depth: 0,
            Element: element
        })

    } catch as e {
        ; 添加错误信息到结果
        scanResults.Push({
            Path: "ERROR: " . e.message,
            Type: "Error",
            Name: "",
            Value: "",
            AutomationId: "",
            ClassName: "",
            Depth: 0,
            Element: ""
        })
    }
}

; UIA诊断函数
DiagnoseUIA(*) {
    global listBox, windows, infoText, UIA_AVAILABLE

    infoText.Value := "=== UIA 诊断测试 ===`n正在进行诊断...`n`n"

    ; 测试0：UIA基本状态检查
    if !UIA_AVAILABLE {
        infoText.Value .= "✗ UIA不可用`n"
        infoText.Value .= "  可能原因：UIA库未正确加载或路径错误`n`n"
        infoText.Value .= "=== 诊断完成 ===`n请确保UIA库文件存在且路径正确"
        return
    }

    ; 测试1：UIA基本功能
    try {
        root := UIA.GetRootElement()
        if root {
            infoText.Value .= "✓ UIA根元素获取成功`n"
        } else {
            infoText.Value .= "✗ UIA根元素获取失败`n"
        }
    } catch as e {
        infoText.Value .= "✗ UIA根元素错误: " . e.message . "`n"
    }

    ; 测试2：鼠标位置元素获取
    try {
        element := UIA.ElementFromPoint()
        if element {
            infoText.Value .= "✓ 鼠标位置元素获取成功`n"
            try {
                name := element.Name
                type := element.ControlType
                infoText.Value .= "  - 类型: " . (type ? type : "未知") . "`n"
                infoText.Value .= "  - 名称: " . (name ? name : "无名称") . "`n"
            } catch {
                infoText.Value .= "  - 属性获取失败`n"
            }
        } else {
            infoText.Value .= "✗ 鼠标位置元素获取失败`n"
        }
    } catch as e {
        infoText.Value .= "✗ 鼠标位置元素错误: " . e.message . "`n"
    }

    ; 测试3：选中窗口的UIA测试
    selected := listBox.Value
    if selected && selected <= windows.Length {
        selectedWindow := windows[selected]
        infoText.Value .= "`n=== 选中窗口测试 ===`n"
        infoText.Value .= "窗口: " . selectedWindow.Title . "`n"

        if selectedWindow.Win && selectedWindow.Win != "BasicMode" {
            winElement := selectedWindow.Win

            ; 测试GetChildren
            try {
                children := winElement.GetChildren()
                if children {
                    infoText.Value .= "✓ GetChildren: " . children.Length . " 个子控件`n"

                    ; 显示前5个子控件信息
                    maxShow := Min(children.Length, 5)
                    Loop maxShow {
                        try {
                            child := children[A_Index]
                            childName := ""
                            childType := ""
                            try {
                                childName := child.Name
                            } catch {
                                childName := ""
                            }
                            try {
                                childType := child.ControlType
                            } catch {
                                childType := ""
                            }
                            infoText.Value .= "  " . A_Index . ". " . (childType ? childType : "Unknown") .
                                             (childName ? "[" . childName . "]" : "") . "`n"
                        } catch {
                            infoText.Value .= "  " . A_Index . ". [获取失败]`n"
                        }
                    }
                } else {
                    infoText.Value .= "✗ GetChildren: 返回空`n"
                }
            } catch as e {
                infoText.Value .= "✗ GetChildren错误: " . e.message . "`n"
            }

            ; 测试FindAll获取所有后代
            try {
                descendants := winElement.FindAll(UIA.TrueCondition, UIA.TreeScope.Descendants)
                if descendants {
                    infoText.Value .= "✓ FindAll(Descendants): " . descendants.Length . " 个后代控件`n"
                } else {
                    infoText.Value .= "✗ FindAll(Descendants): 返回空`n"
                }
            } catch as e {
                infoText.Value .= "✗ FindAll(Descendants)错误: " . e.message . "`n"
            }

        } else {
            infoText.Value .= "✗ 窗口不支持UIA或为基本模式`n"
        }
    } else {
        infoText.Value .= "`n请先选择一个窗口进行测试`n"
    }

    infoText.Value .= "`n=== 诊断完成 ===`n"
    infoText.Value .= "如果大部分测试失败，可能需要：`n"
    infoText.Value .= "1. 以管理员身份运行程序`n"
    infoText.Value .= "2. 检查UIA库版本`n"
    infoText.Value .= "3. 尝试重启目标程序"
}

; 备用扫描方法 - 不依赖UIA
ScanBasicWindowInfo(selectedWindow) {
    global scanResults, infoText

    scanResults := []
    hwnd := selectedWindow.Hwnd

    infoText.Value := "正在使用基本方法扫描窗口信息..."

    try {
        ; 获取窗口基本信息
        title := WinGetTitle(hwnd)
        className := WinGetClass(hwnd)
        processName := WinGetProcessName(hwnd)

        ; 获取窗口位置和大小
        WinGetPos(&x, &y, &w, &h, hwnd)

        ; 获取窗口状态
        minMax := WinGetMinMax(hwnd)
        state := minMax == -1 ? "最小化" : (minMax == 1 ? "最大化" : "正常")

        ; 添加窗口信息到结果
        scanResults.Push({
            Path: "窗口信息",
            Type: "Window",
            Name: title,
            Value: "位置: (" . x . ", " . y . ") 大小: " . w . "x" . h,
            AutomationId: "HWND:" . hwnd,
            ClassName: className,
            Depth: 0,
            Element: ""
        })

        ; 尝试枚举子窗口
        try {
            childWindows := WinGetControls(hwnd)
            if childWindows && childWindows.Length > 0 {
                for i, childHwnd in childWindows {
                    try {
                        childClass := WinGetClass(childHwnd)
                        childText := ControlGetText(childHwnd)

                        scanResults.Push({
                            Path: "  子控件 " . i,
                            Type: "Control",
                            Name: childText ? childText : "[无文本]",
                            Value: "",
                            AutomationId: "HWND:" . childHwnd,
                            ClassName: childClass,
                            Depth: 1,
                            Element: ""
                        })
                    } catch {
                        continue
                    }
                }
            }
        } catch {
            ; 无法获取子控件
        }

        ; 显示结果
        info := "=== 基本窗口信息扫描结果 ===`n"
        info .= "窗口: " . selectedWindow.Title . "`n"
        info .= "找到项目: " . scanResults.Length . " 个`n`n"

        info .= "=== 详细信息 ===`n"
        for i, item in scanResults {
            info .= i . ". " . item.Path . "`n"
            info .= "   类型: " . item.Type . "`n"
            info .= "   名称: " . item.Name . "`n"
            info .= "   值: " . item.Value . "`n"
            info .= "   类名: " . item.ClassName . "`n"
            info .= "`n"
        }

        info .= "注意：这是基本扫描模式，无法获取详细的UIA控件信息。`n"
        info .= "要获取完整控件信息，请：`n"
        info .= "1. 以管理员身份运行程序`n"
        info .= "2. 确保UIA库正确安装`n"
        info .= "3. 尝试其他支持UIA的程序"

        infoText.Value := info

    } catch as e {
        infoText.Value := "基本扫描失败: " . e.message
    }
}
