#Requires AutoHotkey v2.0
#Include UIA-v2-1.1.0\Lib\UIA.ahk

global windows := [], scanResults := []

CreateMainGUI()

CreateMainGUI() {
    global listBox, infoText, editSearch, textStat, windows
    winGui := Gui("+Resize", "UIA 控件结构检查器")

    winGui.AddText("x10 y10 w300 h20", "选择目标窗口：")
    listBox := winGui.AddListBox("x10 y30 w300 h100 vlistBox")

    btnRefresh := winGui.AddButton("x320 y30 w100 h30", "刷新窗口")
    btnScan := winGui.AddButton("x320 y70 w100 h30", "扫描结构")
    btnExport := winGui.AddButton("x320 y110 w100 h30", "导出结果")

    winGui.AddText("x10 y140 w300 h20", "搜索关键词（Name 或 Type）：")
    editSearch := winGui.AddEdit("x10 y160 w300 h30")
    btnSearch := winGui.AddButton("x320 y160 w100 h30", "搜索")

    winGui.AddText("x10 y200 w420 h20", "控件信息/搜索结果：")
    infoText := winGui.AddEdit("x10 y220 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnStat := winGui.AddButton("x10 y380 w200 h30", "类型统计")
    textStat := winGui.AddEdit("x10 y420 w420 h150 +ReadOnly +Multi +HScroll +VScroll")

    btnMonitor := winGui.AddButton("x220 y380 w210 h30", "开启鼠标悬停监控")

    btnRefresh.OnEvent("Click", RefreshWindowList)
    btnScan.OnEvent("Click", ScanUIA)
    btnExport.OnEvent("Click", ExportResult)
    btnSearch.OnEvent("Click", SearchControls)
    btnStat.OnEvent("Click", ShowControlTypeStat)
    btnMonitor.OnEvent("Click", StartMouseMonitor)

    RefreshWindowList()
    winGui.Show("w450 h600")
}

RefreshWindowList(*) {
    global windows, listBox
    windows := []
    listBox.Delete()

    ; Get all window handles using AutoHotkey's WinGetList
    for hwnd in WinGetList() {
        try {
            title := WinGetTitle(hwnd)
            if title != "" {
                ; Create a UIA element from the window handle
                winElement := UIA.ElementFromHandle(hwnd)
                windows.Push({Title: title, Win: winElement, Hwnd: hwnd})
                listBox.Add(title)
            }
        } catch {
            ; Skip windows that can't be accessed via UIA
            continue
        }
    }
}

ScanUIA(*) {
    global listBox, windows, scanResults, infoText
    selected := listBox.Value
    if !selected {
        MsgBox("请选择一个窗口")
        return
    }

    scanResults := []
    win := windows[selected].Win
    root := win.Element
    TraverseElement(root)
    info := ""
    for ctrl in scanResults {
        info .= ctrl.Path " | " ctrl.Type " | " ctrl.Name "`n"
    }
    infoText.Value := info
}

TraverseElement(element, path := "") {
    global scanResults
    name := element.Name
    type := element.ControlType
    fullPath := path "/" type (name ? " [" name "]" : "")
    scanResults.Push({Path: fullPath, Type: type, Name: name, Element: element})
    for child in element.FindAllChildren() {
        TraverseElement(child, fullPath)
    }
}

ExportResult(*) {
    global scanResults
    if scanResults.Length = 0 {
        MsgBox("请先进行结构扫描")
        return
    }
    FileSelect := FileSelect("S", , "保存为...", "Text Files (*.txt)")
    if FileSelect {
        txt := ""
        for item in scanResults {
            txt .= item.Path " | " item.Type " | " item.Name "`n"
        }
        FileAppend(txt, FileSelect, "UTF-8")
        MsgBox("导出完成")
    }
}

SearchControls(*) {
    global scanResults, editSearch, infoText
    keyword := editSearch.Value
    if keyword = "" {
        MsgBox("请输入关键词")
        return
    }
    matches := ""
    for item in scanResults {
        if InStr(item.Name, keyword) || InStr(item.Type, keyword)
            matches .= item.Path " | " item.Type " | " item.Name "`n"
    }
    infoText.Value := matches != "" ? matches : "未找到匹配控件"
}

StartMouseMonitor(*) {
    SetTimer(WatchUnderMouse, 200)
    MsgBox("鼠标悬停监控已启动（每 200ms 更新一次）")
}

WatchUnderMouse() {
    global infoText
    try {
        el := UIA.ElementFromPoint()
        if el {
            infoText.Value := "Name: " el.Name "`nType: " el.ControlType "`nValue: " el.Value "`nAutomationId: " el.AutomationId
        }
    } catch {
        infoText.Value := "无法获取控件信息"
    }
}

ShowControlTypeStat(*) {
    global scanResults, textStat
    stat := Map()
    for item in scanResults {
        type := item.Type
        stat[type] := stat.Has(type) ? stat[type] + 1 : 1
    }
    list := []
    for k, v in stat {
        list.Push({Type: k, Count: v})
    }
    SortArrayByCount(list)
    output := ""
    for item in list {
        output .= item.Type ": " item.Count "`n"
    }
    textStat.Value := output
}

SortArrayByCount(arr) {
    if !arr || arr.Length <= 1
        return arr
    n := arr.Length
    Loop n - 1 {
        i := A_Index
        Loop n - i {
            j := A_Index
            if (j + 1) > n
                continue
            a := arr[j]
            b := arr[j + 1]
            if a.HasProp("Count") && b.HasProp("Count") {
                if a.Count < b.Count {
                    temp := arr[j]
                    arr[j] := arr[j + 1]
                    arr[j + 1] := temp
                }
            }
        }
    }
    return arr
}
